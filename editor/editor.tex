%%%%%%%%%%%%%%%%%%%%%%%% editor.tex %%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% sample root file for the contributions of a "contributed volume"
%
% Use this file as a template for your own input.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Springer %%%%%%%%%%%%%%%%%%%%%%%%%%


% RECOMMENDED %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\documentclass[graybox, envcountchap]{svmult}

% choose options for [] as required from the list
% in the Reference Guide

%\usepackage{type1cm}        % activate if the above 3 fonts are 
                             % not available on your system

\usepackage{makeidx}         % allows index generation
\usepackage{graphicx}        % standard LaTeX graphics tool
                             % when including figure files
\usepackage{multicol}        % used for the two-column index
\usepackage[bottom]{footmisc}% places footnotes at page bottom

% Font packages
\usepackage{newtxtext}       %
\usepackage[varvw]{newtxmath}       % selects Times Roman as basic font

% Additional packages for compatibility
\usepackage{subcaption}      % for subfigure environment
\usepackage{booktabs}        % for better tables
\usepackage{xcolor}          % for colors
\usepackage{multirow}        % for multirow tables
\usepackage{amsmath}         % AMS math package
\usepackage{amsthm}          % for theorem environments
% Note: amssymb and amsfonts conflict with newtxmath, so we load them conditionally
\usepackage{algorithm}       % for algorithms
\usepackage{algorithmicx}    % for algorithmic pseudocode
\usepackage{algpseudocode}   % for pseudocode
\usepackage{listings}        % for code listings
\usepackage{tabularx}        % for flexible tables
\usepackage{array}           % for array and tabular extensions
\usepackage{textcomp}        % for text companion symbols

\usepackage{pdfpages}        % insert manuscripts in PDF (used by Chapter 4)
% see the list of further useful packages in the Reference Guide







% Additional packages that may be needed by specific chapters
\usepackage[title]{appendix}  % for appendices
\usepackage{manyfoot}         % for multiple footnotes
\usepackage[mathscr]{euscript} % for script fonts
\usepackage{colortbl}         % for colored tables

% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% %% Compatibility for Chapter 1 (Andong Wang)
% When enabling Chapter 1, uncomment the following:
% \usepackage{bm}              % bold math symbols
% \usepackage{stmaryrd}        % additional math symbols
% \DeclareMathOperator*{\argmin}{argmin}
% \DeclareMathOperator*{\argmax}{argmax}
%
% Note: preambleWAD.tex contains old packages that conflict with modern setup
% The essential functionality is provided above without conflicts
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\makeindex             % used for the subject index
                       % please use the style svind.ist with
                       % your makeindex program

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\begin{document}

\frontmatter%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\include{dedication}
\include{foreword}
\include{preface}
\include{acknowledgement}

\tableofcontents
\include{contriblist}
\include{acronym}


\mainmatter%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% \include{part}
% %Chapter 1 --- Andong (complete)
% \include{part01}
% \include{author001}
% \include{author002}


% %Chapter 2  --- Haotong
% \include{part2}
% % \include{author3}
% \include{editor/Chapter 2/3/sn-article}
% \include{editor/Chapter 2/4/paper}
% \include{editor/Chapter 2/5/Black_box_attacks_on_LLMs_with_RL_SPRINGER}
% \include{editor/Chapter 2/6/author}
% \include{editor/Chapter 2/7/authorsample}

%Chapter 3 --- Peilin
\include{part03}
\include{author301}

%Chapter 4  --- Kentaroh
% \includepdf[pages=-]{editor/Chapter 4/1/Leon_1.pdf}
%\include{part4}
%\include{author3}



% Chapter 4
% \include{Chapter 4/1/...}

\backmatter%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\appendix
\include{appendix}
\include{glossary}
\printindex

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\end{document}
