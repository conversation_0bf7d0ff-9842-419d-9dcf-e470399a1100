\contentsline {part}{Part\ I\hspace {\betweenumberspace }Part Title}{1}{}%
\contentsline {part}{Part\ II\hspace {\betweenumberspace }Tensor Models for Machine Learning: Empowering Efficiency, Interpretability, and Reliability}{3}{}%
\contentsline {titlech}{\numberline {1}EM Algorithm for Tensor Network Logistic Regression based on P\'{o}lya-Gamma Augmentation}{5}{}%
\authcount {1}
\contentsline {authorch}{<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>}{5}{}%
\contentsline {section}{\numberline {1.1}Introduction}{5}{}%
\contentsline {section}{\numberline {1.2}Derivation of auxiliary function by PG augmentation}{8}{}%
\contentsline {subsection}{\numberline {1.2.1}Theoretical tools for PG augmentation}{8}{}%
\contentsline {subsection}{\numberline {1.2.2}Derivation of auxiliary function}{9}{}%
\contentsline {section}{\numberline {1.3}TN Logistic Regression}{10}{}%
\contentsline {subsection}{\numberline {1.3.1}EM algorithm}{10}{}%
\contentsline {subsection}{\numberline {1.3.2}Update core tensors by solving sub-optimization problems}{11}{}%
\contentsline {section}{\numberline {1.4}Experimental Results}{12}{}%
\contentsline {subsection}{\numberline {1.4.1}Optimization algorithm}{13}{}%
\contentsline {subsection}{\numberline {1.4.2}Evaluation as a classifier}{13}{}%
\contentsline {section}{\numberline {1.5}Conclusion}{14}{}%
\contentsline {section}{References}{14}{}%
\contentsline {section}{References}{14}{}%
\contentsline {titlech}{\numberline {2}Reproducibility Analysis for Results of Coupled Tensor Decompositions Based on Federated Learning}{17}{}%
\authcount {4}
\contentsline {authorch}{Yukai Cai\unskip {} \and Hang Liu\unskip {} \and Xiulin Wang\unskip {} \and Fengyu Cong\unskip {}}{17}{}%
\contentsline {section}{\numberline {2.1}Introduction}{18}{}%
\contentsline {section}{\numberline {2.2}Methods}{20}{}%
\contentsline {subsection}{\numberline {2.2.1}Coupled tensor decomposition}{20}{}%
\contentsline {subsection}{\numberline {2.2.2}A Coupled Nonnegative CANDECOMP/PARAFAC Decomposition Based on Federated Learning}{21}{}%
\contentsline {subsection}{\numberline {2.2.3}Reproducibility analysis based on tensor spectral clustering}{21}{}%
\contentsline {section}{\numberline {2.3}Experiments and results}{22}{}%
\contentsline {subsection}{\numberline {2.3.1}Iteration stopping conditions}{22}{}%
