\relax 
\immediate\closeout\minitoc
\let \MiniTOC =N
\citation{stoudenmire2016supervised}
\citation{wang2023tensor}
\citation{novikov2015tensorizing}
\citation{stoudenmire2016supervised}
\citation{novikov2021tensor}
\citation{wall2022tensor}
\citation{rieser2023tensor}
\citation{stoudenmire2016supervised}
\@writefile{toc}{\contentsline {titlech}{\numberline {1}EM Algorithm for Tensor Network Logistic Regression based on P\'{o}lya-Gamma Augmentation}{3}{}\protected@file@percent }
\@writefile{toc}{\authcount {1}}
\@writefile{toc}{\contentsline {authorch}{<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {1.1}Introduction}{3}{}\protected@file@percent }
\citation{stoudenmire2016supervised}
\citation{oseledets2011tensor}
\@writefile{lof}{\contentsline {figure}{\numberline {1.1}{\ignorespaces TN and TN regression model}}{4}{}\protected@file@percent }
\newlabel{fig:TN}{{1.1}{4}{}{figure.1.1}{}}
\newlabel{eq:l2_loss}{{1.4}{4}{}{equation.1.4}{}}
\citation{sun2016majorization}
\citation{polson2013bayesian}
\citation{scott2013expectation}
\citation{polson2013bayesian}
\newlabel{eq:Am}{{1.7}{5}{}{equation.1.7}{}}
\newlabel{eq:logistic_loss}{{1.8}{5}{}{equation.1.8}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1.2}{\ignorespaces Comparison of algorithms in TN least squares regression (TNLSR)}}{6}{}\protected@file@percent }
\newlabel{fig:opt_l2}{{1.2}{6}{}{figure.1.2}{}}
\@writefile{toc}{\contentsline {section}{\numberline {1.2}Derivation of auxiliary function by PG augmentation}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2.1}Theoretical tools for PG augmentation}{6}{}\protected@file@percent }
\newlabel{eq:PG_dist}{{1.9}{6}{}{equation.1.9}{}}
\newlabel{eq:PG_expectation}{{1.10}{6}{}{equation.1.10}{}}
\newlabel{eq:PG_theorem}{{1.11}{6}{}{equation.1.11}{}}
\newlabel{eq:PG_logistic_to_gauss}{{1.12}{6}{}{equation.1.12}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2.2}Derivation of auxiliary function}{7}{}\protected@file@percent }
\newlabel{eq:aux_func}{{1.15}{7}{}{equation.1.15}{}}
\newlabel{eq:upperbound}{{1.17}{7}{}{equation.1.17}{}}
\newlabel{eq:sum_of_WLS}{{1.18}{7}{}{equation.1.18}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1.3}{\ignorespaces Logistic loss (negative log-likelihood) and its auxiliary function}}{8}{}\protected@file@percent }
\newlabel{fig:upper_bound}{{1.3}{8}{}{figure.1.3}{}}
\@writefile{toc}{\contentsline {section}{\numberline {1.3}TN Logistic Regression}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3.1}EM algorithm}{8}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces EM algorithm for TN logistic regression}}{9}{}\protected@file@percent }
\newlabel{alg:TNEM}{{1}{9}{}{algorithm.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1.4}{\ignorespaces Forward sweep}}{9}{}\protected@file@percent }
\newlabel{fig:fsweep}{{1.4}{9}{}{figure.1.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3.2}Update core tensors by solving sub-optimization problems}{9}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1.5}{\ignorespaces Variants of regression models}}{10}{}\protected@file@percent }
\newlabel{fig:TN_sub}{{1.5}{10}{}{figure.1.5}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1.6}{\ignorespaces Behavior of MNIST learning process}}{11}{}\protected@file@percent }
\newlabel{fig:opt_behav}{{1.6}{11}{}{figure.1.6}{}}
\@writefile{toc}{\contentsline {section}{\numberline {1.4}Experimental Results}{11}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.4.1}Optimization algorithm}{11}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.4.2}Evaluation as a classifier}{11}{}\protected@file@percent }
\bibcite{novikov2015tensorizing}{1}
\bibcite{novikov2021tensor}{2}
\@writefile{lof}{\contentsline {figure}{\numberline {1.7}{\ignorespaces Behavior of MNIST classification accuracy}}{12}{}\protected@file@percent }
\newlabel{fig:acc_behav}{{1.7}{12}{}{figure.1.7}{}}
\@writefile{lot}{\contentsline {table}{\numberline {1.1}{\ignorespaces Comparison of classification accuracy.}}{12}{}\protected@file@percent }
\newlabel{tab:comp}{{1.1}{12}{}{table.1.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {1.5}Conclusion}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{References}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{References}{12}{}\protected@file@percent }
\@mtwritefile{\contentsline {mtchap}{References}{12}}
\bibcite{oseledets2011tensor}{3}
\bibcite{polson2013bayesian}{4}
\bibcite{rieser2023tensor}{5}
\bibcite{scott2013expectation}{6}
\bibcite{stoudenmire2016supervised}{7}
\bibcite{sun2016majorization}{8}
\bibcite{wall2022tensor}{9}
\bibcite{wang2023tensor}{10}
\@setckpt{author001}{
\setcounter{page}{14}
\setcounter{equation}{28}
\setcounter{enumi}{0}
\setcounter{enumii}{0}
\setcounter{enumiii}{0}
\setcounter{enumiv}{10}
\setcounter{footnote}{0}
\setcounter{mpfootnote}{0}
\setcounter{part}{1}
\setcounter{section}{5}
\setcounter{subsection}{0}
\setcounter{subsubsection}{0}
\setcounter{paragraph}{0}
\setcounter{subparagraph}{0}
\setcounter{figure}{7}
\setcounter{table}{1}
\setcounter{minitocdepth}{0}
\setcounter{chapter}{1}
\setcounter{theorem}{0}
\setcounter{case}{0}
\setcounter{conjecture}{0}
\setcounter{corollary}{0}
\setcounter{definition}{0}
\setcounter{example}{0}
\setcounter{exercise}{0}
\setcounter{lemma}{0}
\setcounter{note}{0}
\setcounter{problem}{0}
\setcounter{property}{0}
\setcounter{proposition}{0}
\setcounter{question}{0}
\setcounter{solution}{0}
\setcounter{remark}{0}
\setcounter{prob}{0}
\setcounter{merk}{0}
\setcounter{@inst}{1}
\setcounter{@auth}{1}
\setcounter{auco}{1}
\setcounter{contribution}{1}
\setcounter{parentequation}{0}
\setcounter{subfigure}{0}
\setcounter{lofdepth}{1}
\setcounter{subtable}{0}
\setcounter{lotdepth}{1}
\setcounter{float@type}{16}
\setcounter{algorithm}{1}
\setcounter{ALC@unique}{17}
\setcounter{ALC@line}{17}
\setcounter{ALC@rem}{0}
\setcounter{ALC@depth}{0}
\setcounter{ALG@line}{0}
\setcounter{ALG@rem}{0}
\setcounter{ALG@nested}{0}
\setcounter{ALG@Lnr}{2}
\setcounter{ALG@blocknr}{10}
\setcounter{ALG@storecount}{0}
\setcounter{ALG@tmpcounter}{0}
\setcounter{lstnumber}{1}
\setcounter{AM@survey}{0}
\setcounter{@pps}{0}
\setcounter{@ppsavesec}{0}
\setcounter{@ppsaveapp}{0}
\setcounter{lstlisting}{0}
}
