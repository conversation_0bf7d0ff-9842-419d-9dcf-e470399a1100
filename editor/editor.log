This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.22)  31 MAY 2025 15:24
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/GitHub/cai-draft/editor/editor.tex
(/Users/<USER>/Documents/GitHub/cai-draft/editor/editor.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./svmult.cls
Document Class: svmult 2024/03/15 v5.11 
Springer Verlag global LaTeX document class for multi authored books
Class Springer-SVMult Info: extra/valid Springer sub-package 
(Springer-SVMult)           not found in option list - using "global" style.
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
\svparindent=\dimen142
\bibindent=\dimen143
\betweenumberspace=\dimen144
\headlineindent=\dimen145
\minitoc=\write3
\c@minitocdepth=\count270
\c@chapter=\count271
\mottowidth=\dimen146
\svitemindent=\dimen147
\verbatimindent=\dimen148
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 975.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 976.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 977.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 978.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 979.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 980.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 981.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 982.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 983.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 984.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 985.
\tocchpnum=\dimen149
\tocsecnum=\dimen150
\tocsectotal=\dimen151
\tocsubsecnum=\dimen152
\tocsubsectotal=\dimen153
\tocsubsubsecnum=\dimen154
\tocsubsubsectotal=\dimen155
\tocparanum=\dimen156
\tocparatotal=\dimen157
\tocsubparanum=\dimen158
\foot@parindent=\dimen159
\c@theorem=\count272
\c@case=\count273
\c@conjecture=\count274
\c@corollary=\count275
\c@definition=\count276
\c@example=\count277
\c@exercise=\count278
\c@lemma=\count279
\c@note=\count280
\c@problem=\count281
\c@property=\count282
\c@proposition=\count283
\c@question=\count284
\c@solution=\count285
\c@remark=\count286
\c@prob=\count287
\instindent=\dimen160
\figgap=\dimen161
\bildb@x=\box52
\figcapgap=\dimen162
\tabcapgap=\dimen163
\c@merk=\count288
\c@@inst=\count289
\c@@auth=\count290
\c@auco=\count291
\instindent=\dimen164
\authrun=\box53
\authorrunning=\toks17
\tocauthor=\toks18
\titrun=\box54
\titlerunning=\toks19
\toctitle=\toks20
\c@contribution=\count292
LaTeX Info: Redefining \abstract on input line 2385.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/x11nam.def
File: x11nam.def 2024/09/29 v3.02 Predefined colors according to Unix/X11 (UK)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks21
\thm@bodyfont=\toks22
\thm@headfont=\toks23
\thm@notefont=\toks24
\thm@headpunct=\toks25
\thm@preskip=\skip51
\thm@postskip=\skip52
\thm@headsep=\skip53
\dth@everypar=\toks26
LaTeX Info: Redefining \qed on input line 273.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/framed/framed.sty
Package: framed 2011/10/22 v 0.96: framed or shaded text with page breaks
\OuterFrameSep=\skip54
\fb@frw=\dimen165
\fb@frh=\dimen166
\FrameRule=\dimen167
\FrameSep=\dimen168
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/makeidx.sty
Package: makeidx 2021/10/04 v1.0m Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks27
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen169
\Gin@req@width=\dimen170
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count293
\mult@box=\box55
\multicol@leftmargin=\dimen171
\c@unbalance=\count294
\c@collectmore=\count295
\doublecol@number=\count296
\multicoltolerance=\count297
\multicolpretolerance=\count298
\full@width=\dimen172
\page@free=\dimen173
\premulticols=\dimen174
\postmulticols=\dimen175
\multicolsep=\skip55
\multicolbaselineskip=\skip56
\partial@page=\box56
\last@line=\box57
\mc@boxedresult=\box58
\maxbalancingoverflow=\dimen176
\mult@rightbox=\box59
\mult@grightbox=\box60
\mult@firstbox=\box61
\mult@gfirstbox=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\c@minrows=\count299
\c@columnbadness=\count300
\c@finalcolumnbadness=\count301
\last@try=\dimen177
\multicolovershoot=\dimen178
\multicolundershoot=\dimen179
\mult@nat@firstbox=\box99
\colbreak@box=\box100
\mc@col@check@num=\count302
) (/usr/local/texlive/2025/texmf-dist/tex/latex/footmisc/footmisc.sty
Package: footmisc 2024/12/24 v6.0g a miscellany of footnote facilities
\FN@temptoken=\toks28
\footnotemargin=\dimen180
\@outputbox@depth=\dimen181
Package footmisc Info: Declaring symbol style bringhurst on input line 699.
Package footmisc Info: Declaring symbol style chicago on input line 707.
Package footmisc Info: Declaring symbol style wiley on input line 716.
Package footmisc Info: Declaring symbol style lamport-robust on input line 727.
Package footmisc Info: Declaring symbol style lamport* on input line 747.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 768.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2024/04/01 v1.744(Michael Sharpe) latex and unicode latex support for TeXGyreTermesX
 `newtxtext' v1.744, 2024/04/01 Text macros taking advantage of TeXGyre Termes and its extensions (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/xpatch/xpatch.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count303
\l__pdf_internal_box=\box101
))
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count304
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks29
\XKV@tempa@toks=\toks30
)
\XKV@depth=\count305
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count306
\xs_countb=\count307
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/carlisle/scalefnt.sty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 24.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count308
\ntx@cnt=\count309
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
\tx@sixem=\dimen182
\tx@y=\dimen183
\tx@x=\dimen184
\tx@tmpdima=\dimen185
\tx@tmpdimb=\dimen186
\tx@tmpdimc=\dimen187
\tx@tmpdimd=\dimen188
\tx@tmpdime=\dimen189
\tx@tmpdimf=\dimen190
\tx@dimA=\dimen191
\tx@dimAA=\dimen192
\tx@dimB=\dimen193
\tx@dimBB=\dimen194
\tx@dimC=\dimen195
LaTeX Info: Redefining \oldstylenums on input line 902.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/09/22 v1.754
 `newtxmath' v1.754, 2024/09/22 Math macros based originally on txfonts (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip57

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks31
\ex@=\dimen196
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen197
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count310
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count311
\leftroot@=\count312
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count313
\DOTSCASE@=\count314
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box102
\strutbox@=\box103
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen198
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count315
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count316
\dotsspace@=\muskip17
\c@parentequation=\count317
\dspbrk@lvl=\count318
\tag@help=\toks32
\row@=\count319
\column@=\count320
\maxfields@=\count321
\andhelp@=\toks33
\eqnshift@=\dimen199
\alignsep@=\dimen256
\tagshift@=\dimen257
\tagwidth@=\dimen258
\totwidth@=\dimen259
\lineht@=\dimen260
\@envbody=\toks34
\multlinegap=\skip58
\multlinetaggap=\skip59
\mathdisplay@stack=\toks35
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count322
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count323
\tx@IsAlNum=\count324
\tx@tA=\toks36
\tx@tB=\toks37
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/qhv/m/n --> OT1/qhv/b/n on input line 412.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 423.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/ntxtt/m/n --> OT1/ntxtt/b/n on input line 426.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 535.
\symlettersA=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 582.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 605.
\symAMSm=\mathgroup5
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 630.
\symsymbolsC=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 651.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 665.
\symlargesymbolsTXA=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'
(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 679.
\tx@sbptoks=\toks38
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 903.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 904.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 905.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 910.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 911.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 913.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 915.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 919.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 920.
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 966.
LaTeX Font Info:    Redeclaring math accent \dot on input line 991.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 992.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2057.
LaTeX Info: Redefining \Bbbk on input line 2847.
LaTeX Info: Redefining \not on input line 2995.
LaTeX Info: Redefining \textsquare on input line 3025.
LaTeX Info: Redefining \openbox on input line 3027.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen261
\captionmargin=\dimen262
\caption@leftmargin=\dimen263
\caption@rightmargin=\dimen264
\caption@width=\dimen265
\caption@indent=\dimen266
\caption@parindent=\dimen267
\caption@hangindent=\dimen268
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\captionstyle \ifx \@captype \fig@type \vskip \figcapgap \fi \setbox \@tempboxa \hbox {{\floatlegendstyle #1\floatcounterend }\capstrut #2}\ifdim \wd \@tempboxa >\hsize {\floatlegendstyle #1\floatcounterend }\capstrut #2\par \else \hbox to\hsize {\leftlegendglue \unhbox \@tempboxa \hfil }\fi \ifx \@captype \fig@type \else \vskip \tabcapgap \fi  on input line 1175.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count325
\c@continuedfloat=\count326
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count327
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count328
) (/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen269
\lightrulewidth=\dimen270
\cmidrulewidth=\dimen271
\belowrulesep=\dimen272
\belowbottomsep=\dimen273
\aboverulesep=\dimen274
\abovetopsep=\dimen275
\cmidrulesep=\dimen276
\cmidrulekern=\dimen277
\defaultaddspace=\dimen278
\@cmidla=\count329
\@cmidlb=\count330
\@aboverulesep=\dimen279
\@belowrulesep=\dimen280
\@thisruleclass=\count331
\@lastruleclass=\count332
\@thisrulewidth=\dimen281
) (/usr/local/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip60
\multirow@cntb=\count333
\multirow@dima=\skip61
\bigstrutjot=\dimen282
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count334
\float@exts=\toks39
\float@box=\box104
\@float@everytoks=\toks40
\@floatcapt=\box105
)
\@float@every@algorithm=\toks41
\c@algorithm=\count335
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count336
\c@ALG@rem=\count337
\c@ALG@nested=\count338
\ALG@tlm=\skip62
\ALG@thistlm=\skip63
\c@ALG@Lnr=\count339
\c@ALG@blocknr=\count340
\c@ALG@storecount=\count341
\c@ALG@tmpcounter=\count342
\ALG@tmplength=\skip64
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

Document Style - pseudocode environments for use with the `algorithmicx' style
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count343
\lst@gtempboxa=\box106
\lst@token=\toks42
\lst@length=\count344
\lst@currlwidth=\dimen283
\lst@column=\count345
\lst@pos=\count346
\lst@lostspace=\dimen284
\lst@width=\dimen285
\lst@newlines=\count347
\lst@lineno=\count348
\lst@maxwidth=\dimen286
 (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count349
\lst@skipnumbers=\count350
\lst@framebox=\box107
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen287
\ar@mcellbox=\box108
\extrarowheight=\dimen288
\NC@list=\toks43
\extratabsurround=\skip65
\backup@length=\skip66
\ar@cellbox=\box109
)
\TX@col@width=\dimen289
\TX@old@table=\dimen290
\TX@old@col=\dimen291
\TX@target=\dimen292
\TX@delta=\dimen293
\TX@cols=\count351
\TX@ftn=\toks44
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pdfpages/pdfpages.sty
Package: pdfpages 2025/01/30 v0.6e Insert pages of external PDF documents (AM)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count352
\calc@Bcount=\count353
\calc@Adimen=\dimen294
\calc@Bdimen=\dimen295
\calc@Askip=\skip67
\calc@Bskip=\skip68
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count354
\calc@Cskip=\skip69
) (/usr/local/texlive/2025/texmf-dist/tex/latex/eso-pic/eso-pic.sty
Package: eso-pic 2023/05/03 v3.0c eso-pic (RN)
\ESO@tempdima=\dimen296
\ESO@tempdimb=\dimen297
)
\AM@pagewidth=\dimen298
\AM@pageheight=\dimen299
\AM@fboxrule=\dimen300
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pdfpages/pppdftex.def
File: pppdftex.def 2025/01/30 v0.6e Pdfpages driver for pdfTeX (AM)
)
\pdfpages@includegraphics@status=\count355
\AM@pagebox=\box110
\AM@global@opts=\toks45
\AM@pagecnt=\count356
\AM@toc@title=\toks46
\AM@lof@heading=\toks47
\c@AM@survey=\count357
\AM@templatesizebox=\box111
) (/usr/local/texlive/2025/texmf-dist/tex/latex/appendix/appendix.sty
Package: appendix 2020/02/08 v1.2c extra appendix facilities
\c@@pps=\count358
\c@@ppsavesec=\count359
\c@@ppsaveapp=\count360
) (/usr/local/texlive/2025/texmf-dist/tex/latex/ncctools/manyfoot.sty
Package: manyfoot 2019/08/03 v1.11 Many Footnote Levels Package (NCC)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/ncctools/nccfoots.sty
Package: nccfoots 2005/02/03 v1.2 NCC Footnotes Package (NCC)
)
\MFL@columnwidth=\dimen301
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/euscript.sty
Package: euscript 2009/06/22 v3.00 Euler Script fonts
LaTeX Font Info:    Overwriting math alphabet `\EuScript' in version `bold'
(Font)                  U/eus/m/n --> U/eus/b/n on input line 33.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
\everycr=\toks48
\minrowclearance=\skip70
\rownum=\count361
)
\@indexfile=\write4
\openout4 = `editor.idx'.


Writing index file editor.idx
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input line 78.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 10.0pt on input line 78.
 (./editor.aux (./dedication.aux) (./foreword.aux) (./preface.aux) (./acknowledgement.aux) (./contriblist.aux) (./acronym.aux) (./part03.aux) (./author301.aux) (./appendix.aux) (./glossary.aux))
\openout1 = `editor.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 78.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line 78.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 78.
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 78.
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 78.
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 78.
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 78.
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 78.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 78.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 78.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line 78.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 78.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 78.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input line 78.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 78.
 (/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count362
\scratchdimen=\dimen302
\scratchbox=\box112
\nofMPsegments=\count363
\nofMParguments=\count364
\everyMPshowfont=\toks49
\MPscratchCnt=\count365
\MPscratchDim=\dimen303
\MPnumerator=\count366
\makeMPintoPDFobject=\count367
\everyMPtoPDFconversion=\toks50
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
\c@mv@tabular=\count368
\c@mv@boldtabular=\count369
LaTeX Info: Command `\dddot' is already robust on input line 78.
LaTeX Info: Command `\ddddot' is already robust on input line 78.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: listings package is loaded.
Package caption Info: End \AtBeginDocument code.
\c@lstlisting=\count370
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape.sty
Package: pdflscape 2022-10-27 v0.13 Display of landscape pages in PDF
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape-nometadata.sty
Package: pdflscape-nometadata 2022-10-28 v0.13 Display of landscape pages in PDF (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/lscape.sty
Package: lscape 2020/05/28 v3.02 Landscape Pages (DPC)
)
Package pdflscape Info: Auto-detected driver: pdftex on input line 81.
))
\openout2 = `dedication.aux'.

 (./dedication.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 12.0pt on input line 9.


[5






{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc}])
\openout2 = `foreword.aux'.

 (./foreword.tex

[6






]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 16.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 16.0pt on input line 9.
LaTeX Font Info:    Trying to load font information for OT1+minntx on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2024/04/09 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 10.0pt on input line 10.

Overfull \hbox (3.95215pt too wide) in paragraph at lines 10--11
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 fore-word.tex \OT1/minntx/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

)

LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.5pt on input line 83.
[7]
\openout2 = `preface.aux'.

 (./preface.tex

[8





])

[9]
\openout2 = `acknowledgement.aux'.

 (./acknowledgement.tex

[10





])

[11]

[12




] (./editor.toc
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 10.0pt on input line 1.
)
\tf@toc=\write5
\openout5 = `editor.toc'.



[13]
\openout2 = `contriblist.aux'.

 (./contriblist.tex

[14



])

[15]
\openout2 = `acronym.aux'.

 (./acronym.tex

[16





]
Overfull \hbox (3.22217pt too wide) in paragraph at lines 10--11
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 acronym.tex \OT1/minntx/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

LaTeX Font Info:    Trying to load font information for OT1+ntxtt on input line 12.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtt.fd
File: ot1ntxtt.fd 2012/04/20 v3.1
)
LaTeX Font Info:    Font shape `OT1/ntxtt/m/n' will be
(Font)              scaled to size 10.0pt on input line 12.
)

[17]

[18




]
\openout2 = `part03.aux'.

 (./part03.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 18.0pt on input line 10.


[1



]

[2])
\openout2 = `author301.aux'.

 (./author301.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 14.0pt on input line 55.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 14.0pt on input line 55.
LaTeX Font Info:    Calculating math sizes for size <8.5> on input line 55.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 61.


[3





]

[4]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 11.0pt on input line 84.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 11.0pt on input line 84.


[5]

[6]
<fig301/1.png, id=71, 1592.95125pt x 531.9875pt>
File: fig301/1.png Graphic file (type png)
<use fig301/1.png>
Package pdftex.def Info: fig301/1.png  used on input line 122.
(pdftex.def)             Requested size: 299.60547pt x 100.05586pt.


[7 <./fig301/1.png>]
<fig301/2.png, id=77, 939.51pt x 855.195pt>
File: fig301/2.png Graphic file (type png)
<use fig301/2.png>
Package pdftex.def Info: fig301/2.png  used on input line 153.
(pdftex.def)             Requested size: 299.60547pt x 272.71541pt.


[8]
<fig301/3.png, id=81, 897.3525pt x 445.665pt>
File: fig301/3.png Graphic file (type png)
<use fig301/3.png>
Package pdftex.def Info: fig301/3.png  used on input line 167.
(pdftex.def)             Requested size: 332.89723pt x 165.32867pt.


[9 <./fig301/2.png>]

[10 <./fig301/3.png>]
<fig301/4a.png, id=90, 479.7925pt x 336.25626pt>
File: fig301/4a.png Graphic file (type png)
<use fig301/4a.png>
Package pdftex.def Info: fig301/4a.png  used on input line 205.
(pdftex.def)             Requested size: 159.78925pt x 111.98595pt.
<fig301/4b.png, id=91, 469.755pt x 369.38pt>
File: fig301/4b.png Graphic file (type png)
<use fig301/4b.png>
Package pdftex.def Info: fig301/4b.png  used on input line 212.
(pdftex.def)             Requested size: 159.78925pt x 125.64389pt.


[11 <./fig301/4a.png> <./fig301/4b.png>]

[12]

[13]
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 8.5pt on input line 309.
 (./references301.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.5pt on input line 4.


[14]))

[15]

[16




]
\openout2 = `appendix.aux'.

 (./appendix.tex
Appendix A.

Overfull \hbox (5.14217pt too wide) in paragraph at lines 14--15
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 ap-pendix.tex \OT1/minntx/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 7.3pt on input line 24.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
Package epstopdf Info: Source file: <figure.eps>
(epstopdf)                    date: 2025-05-31 07:04:40
(epstopdf)                    size: 62739 bytes
(epstopdf)             Output file: <figure-eps-converted-to.pdf>
(epstopdf)                    date: 2025-05-31 07:04:40
(epstopdf)                    size: 13942 bytes
(epstopdf)             Command: <repstopdf --outfile=figure-eps-converted-to.pdf figure.eps>
(epstopdf)             \includegraphics on input line 55.
Package epstopdf Info: Output file is already uptodate.
<figure-eps-converted-to.pdf, id=113, 139.52126pt x 195.73125pt>
File: figure-eps-converted-to.pdf Graphic file (type pdf)
<use figure-eps-converted-to.pdf>
Package pdftex.def Info: figure-eps-converted-to.pdf  used on input line 55.
(pdftex.def)             Requested size: 90.68773pt x 127.22379pt.


[17





]
Overfull \hbox (0.61853pt too wide) in paragraph at lines 67--78
[][]
 []

)

[18 <./figure-eps-converted-to.pdf>]
\openout2 = `glossary.aux'.

 (./glossary.tex)

[19





]
No file editor.ind.
(./editor.aux (./dedication.aux) (./foreword.aux) (./preface.aux) (./acknowledgement.aux) (./contriblist.aux) (./acronym.aux) (./part03.aux) (./author301.aux) (./appendix.aux) (./glossary.aux))
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2020/03/25>
 ***********
 ) 
Here is how much of TeX's memory you used:
 13480 strings out of 473190
 206321 string characters out of 5715800
 625686 words of memory out of 5000000
 36411 multiletter control sequences out of 15000+600000
 620944 words of font info for 156 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 83i,12n,131p,1436b,604s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/NewTXMI.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/NewTXMI5.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/txmiaX.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/txsys.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/txfonts/txtt.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmb.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmr.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmri.pfb>
Output written on editor.pdf (33 pages, 6037060 bytes).
PDF statistics:
 166 PDF objects out of 1000 (max. 8388607)
 102 compressed objects within 2 object streams
 0 named destinations out of 1000 (max. 500000)
 31 words of extra memory for PDF output out of 10000 (max. 10000000)

