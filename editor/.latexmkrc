# Latexmk configuration for Overleaf compatibility
# This file configures latexmk to work similarly to Overleaf

# Use pdflatex as the default engine (Overleaf default)
$pdf_mode = 1;
$postscript_mode = 0;
$dvi_mode = 0;

# Standard pdflatex command (Overleaf style)
$pdflatex = 'pdflatex -interaction=nonstopmode -file-line-error %O %S';

# Configure makeindex to use the Springer style
$makeindex = 'makeindex -s svind.ist %O -o %D %S';

# Maximum number of runs (Overleaf typically uses 3-5)
$max_repeat = 5;

# Clean up auxiliary files
$clean_ext = 'auxlock figlist makefile fls log fdb_latexmk synctex.gz run.xml tex.bak bbl bcf blg idx ilg ind lof lot out toc acn acr alg glg glo gls ist';

# Show used CPU time
$show_time = 1;

# Configure bibtex if needed
$bibtex_use = 1.5;
